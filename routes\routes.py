
from fastapi import APIRouter
from config.mongo import blogsCollection
from model.model import Blog
from serializer.serializer import convertBlog, convertBlogs
from bson import ObjectId

endPoints = APIRouter()

@endPoints.get("/")
def home():
    return{
        "status": "ok",
        "message": "my fast api",
        "ten": "<PERSON>ruong",
        "tuoi": "Nghia lac"
    }

@endPoints.post('/new/blog')
def newBlog(blog:Blog):
    blogsCollection.insert_one(dict(blog))
    return{
        "status" : "ok",
        "message" : "data inserted"
    }

@endPoints.get("/all/blogs")
def getAllBlogs():
    blogs = blogsCollection.find()
    convertedBlogs = convertBlogs(blogs)
    return{
        "status" : "ok",
        "data" : convertedBlogs
    }

@endPoints.get("/blog/{id}")
def getBlog(id : str):
    blog = blogsCollection.find_one({"_id" : ObjectId(id)})
    convertedBlog = convertBlog(blog)
    return{
        "status" : "ok",
        "data" : convertedBlog
    }

@endPoints.patch("update/{id}")
def updateBlog(id: str, blog: Blog):
    blogsCollection.find_one_and_update(
        {"_id" : ObjectId(id)},
        {"$set" : dict(blog)}
    )
    return{
        "status" : "ok",
        "message" : "Data have been updated"
    }

@endPoints.delete("/delete/{id}")
def deleteDoc(id : str):
    blogsCollection.find_one_and_delete({"_id" : ObjectId(id)})
    return{
        "status" : "ok",
        "message" : "Document have bên deleted succussfully"
    }
