
from pymongo.mongo_client import MongoClient
from pymongo.server_api import ServerApi

uri = "mongodb+srv://TruongMongoDB:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0"

# Create a new client and connect to the server
client = MongoClient(uri, server_api=ServerApi('1'))

db = client.myBlogs

blogsCollection = db["Truong Blogs"]
usersCollection = db["Tôi là users"]

# Send a ping to confirm a successful connection
try:
    client.admin.command('ping')
    print("Truong là tao day Pinged your deployment. You successfully connected to MongoDB!")
except Exception as e:
    print(e)